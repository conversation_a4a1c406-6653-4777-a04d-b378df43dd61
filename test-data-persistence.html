<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据持久化功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>数据持久化功能测试</h1>
    
    <div class="test-section">
        <h2>1. 基础功能测试</h2>
        <button class="test-button" onclick="testBasicFunctions()">测试基础功能</button>
        <div id="basic-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. IndexedDB备份测试</h2>
        <button class="test-button" onclick="testIndexedDBBackup()">测试备份功能</button>
        <button class="test-button" onclick="testIndexedDBRecover()">测试恢复功能</button>
        <div id="indexeddb-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 导出导入测试</h2>
        <button class="test-button" onclick="testExport()">测试导出</button>
        <input type="file" id="importFile" accept=".json" style="display:none" onchange="testImport(this.files[0])">
        <button class="test-button" onclick="document.getElementById('importFile').click()">测试导入</button>
        <div id="export-import-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 数据健康检查</h2>
        <button class="test-button" onclick="testDataHealth()">检查数据健康状态</button>
        <div id="health-result" class="result"></div>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/visit-manager.js"></script>
    <script>
        let testVisitManager;
        
        // 初始化测试环境
        function initTest() {
            testVisitManager = new VisitManager();
            console.log('测试环境初始化完成');
        }
        
        // 测试基础功能
        async function testBasicFunctions() {
            const result = document.getElementById('basic-result');
            result.innerHTML = '正在测试基础功能...';
            
            try {
                // 测试数据健康检查
                const healthCheck = testVisitManager.checkDataHealth();
                
                // 测试记录访问
                testVisitManager.recordVisit('test-site-1', '测试网站1', 'external');
                testVisitManager.recordVisit('test-site-2', '测试网站2', 'markdown');
                
                // 测试获取访问记录
                const recentSites = testVisitManager.getRecentSites(5);
                const stats = testVisitManager.getStats();
                
                result.innerHTML = `
                    <div class="success">
                        ✅ 基础功能测试通过<br>
                        • localStorage健康检查: ${healthCheck ? '正常' : '异常'}<br>
                        • 访问记录数量: ${stats.uniqueSites}<br>
                        • 总访问次数: ${stats.totalVisits}<br>
                        • 最近访问: ${recentSites.length} 个网站
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `<div class="error">❌ 基础功能测试失败: ${error.message}</div>`;
            }
        }
        
        // 测试IndexedDB备份
        async function testIndexedDBBackup() {
            const result = document.getElementById('indexeddb-result');
            result.innerHTML = '正在测试IndexedDB备份...';
            
            try {
                const success = await testVisitManager.backupToIndexedDB();
                if (success) {
                    result.innerHTML = '<div class="success">✅ IndexedDB备份测试通过</div>';
                } else {
                    result.innerHTML = '<div class="warning">⚠️ 备份完成但没有数据需要备份</div>';
                }
            } catch (error) {
                result.innerHTML = `<div class="error">❌ IndexedDB备份测试失败: ${error.message}</div>`;
            }
        }
        
        // 测试IndexedDB恢复
        async function testIndexedDBRecover() {
            const result = document.getElementById('indexeddb-result');
            result.innerHTML = '正在测试IndexedDB恢复...';
            
            try {
                const success = await testVisitManager.recoverFromIndexedDB();
                if (success) {
                    result.innerHTML = '<div class="success">✅ IndexedDB恢复测试通过</div>';
                } else {
                    result.innerHTML = '<div class="warning">⚠️ 没有找到可恢复的备份数据</div>';
                }
            } catch (error) {
                result.innerHTML = `<div class="error">❌ IndexedDB恢复测试失败: ${error.message}</div>`;
            }
        }
        
        // 测试导出
        function testExport() {
            const result = document.getElementById('export-import-result');
            result.innerHTML = '正在测试导出功能...';
            
            try {
                testVisitManager.exportData();
                result.innerHTML = '<div class="success">✅ 导出功能测试通过，请检查下载的文件</div>';
            } catch (error) {
                result.innerHTML = `<div class="error">❌ 导出功能测试失败: ${error.message}</div>`;
            }
        }
        
        // 测试导入
        function testImport(file) {
            const result = document.getElementById('export-import-result');
            result.innerHTML = '正在测试导入功能...';
            
            if (!file) {
                result.innerHTML = '<div class="error">❌ 请选择要导入的文件</div>';
                return;
            }
            
            try {
                testVisitManager.importData(file);
                result.innerHTML = '<div class="success">✅ 导入功能测试启动，请查看控制台和Toast消息</div>';
            } catch (error) {
                result.innerHTML = `<div class="error">❌ 导入功能测试失败: ${error.message}</div>`;
            }
        }
        
        // 测试数据健康状态
        async function testDataHealth() {
            const result = document.getElementById('health-result');
            result.innerHTML = '正在检查数据健康状态...';
            
            try {
                const status = await testVisitManager.getDataHealthStatus();
                result.innerHTML = `
                    <div class="success">
                        ✅ 数据健康检查完成<br>
                        • localStorage: ${status.localStorage ? '正常' : '异常'}<br>
                        • 访问历史: ${status.visitHistoryCount} 条记录<br>
                        • IndexedDB: ${status.indexedDBAvailable ? '可用' : '不可用'}<br>
                        • 备份数据: ${status.hasBackupData ? '存在' : '不存在'}<br>
                        • 最后备份: ${status.lastBackupTime ? status.lastBackupTime.toLocaleString() : '无记录'}
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `<div class="error">❌ 数据健康检查失败: ${error.message}</div>`;
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('DOMContentLoaded', initTest);
    </script>
</body>
</html>
