// 验证常用分类Bug修复的脚本
// 在主页面控制台中运行此脚本

console.log('🔍 开始验证常用分类Bug修复...');

// 检查navApp是否存在
if (typeof navApp === 'undefined') {
    console.error('❌ navApp未定义，请确保在主页面运行此脚本');
    throw new Error('navApp not found');
}

// 1. 准备测试数据
console.log('📝 准备测试数据...');
navApp.visitManager.recordVisit("k8s-app", "发布系统", "external");
navApp.visitManager.recordVisit("git-repo", "GitLab", "external");
navApp.visitManager.recordVisit("wiki", "WIKI", "external");

// 2. 测试场景A：从子分类切换到常用（这个应该一直正常）
console.log('\n🧪 测试场景A：从子分类切换到常用');
navApp.switchCategory("code-related"); // 切换到代码管理分类
console.log('  切换到代码管理分类');
navApp.switchCategory("frequent");
console.log('  切换到常用分类');
console.log('  结果 - 当前分类:', navApp.currentCategory);
console.log('  结果 - 显示所有分类模式:', navApp.isShowingAllCategories);

const testA_pass = (navApp.currentCategory === 'frequent' && navApp.isShowingAllCategories === false);
console.log(testA_pass ? '  ✅ 场景A通过' : '  ❌ 场景A失败');

// 3. 测试场景B：从全部分类切换到常用（这是问题场景）
console.log('\n🧪 测试场景B：从全部分类切换到常用（Bug场景）');
navApp.switchCategory("all-categories");
console.log('  切换到全部分类');
console.log('  状态 - 当前分类:', navApp.currentCategory);
console.log('  状态 - 显示所有分类模式:', navApp.isShowingAllCategories);

navApp.switchCategory("frequent");
console.log('  切换到常用分类');
console.log('  结果 - 当前分类:', navApp.currentCategory);
console.log('  结果 - 显示所有分类模式:', navApp.isShowingAllCategories);

// 检查DOM内容
const container = document.querySelector(".sites-container");
const hasWaterfallLayout = container.classList.contains("waterfall-layout");
const hasCategorySections = container.innerHTML.includes("category-section");
const hasCategoryGroupHeader = container.innerHTML.includes("category-group-header");

console.log('  DOM检查 - 容器CSS类:', container.className);
console.log('  DOM检查 - 瀑布流布局:', hasWaterfallLayout);
console.log('  DOM检查 - 包含分类区域:', hasCategorySections);
console.log('  DOM检查 - 包含分类组标题:', hasCategoryGroupHeader);

const testB_pass = (
    navApp.currentCategory === 'frequent' && 
    navApp.isShowingAllCategories === false && 
    !hasWaterfallLayout &&
    !hasCategoryGroupHeader
);
console.log(testB_pass ? '  ✅ 场景B通过' : '  ❌ 场景B失败');

// 4. 测试场景C：视图模式切换（可能触发refreshCurrentView）
console.log('\n🧪 测试场景C：视图模式切换');
const originalViewMode = navApp.isCompactMode;
console.log('  当前视图模式:', originalViewMode ? '紧凑' : '标准');

navApp.toggleViewMode();
console.log('  切换视图模式');

// 等待视图切换完成
setTimeout(() => {
    console.log('  切换后 - 当前分类:', navApp.currentCategory);
    console.log('  切换后 - 显示所有分类模式:', navApp.isShowingAllCategories);
    
    const containerAfter = document.querySelector(".sites-container");
    const hasWaterfallAfter = containerAfter.classList.contains("waterfall-layout");
    const hasCategorySectionsAfter = containerAfter.innerHTML.includes("category-section");
    
    console.log('  切换后 - 瀑布流布局:', hasWaterfallAfter);
    console.log('  切换后 - 包含分类区域:', hasCategorySectionsAfter);
    
    const testC_pass = (
        navApp.currentCategory === 'frequent' && 
        navApp.isShowingAllCategories === false && 
        !hasWaterfallAfter
    );
    console.log(testC_pass ? '  ✅ 场景C通过' : '  ❌ 场景C失败');
    
    // 恢复原始视图模式
    if (navApp.isCompactMode !== originalViewMode) {
        navApp.toggleViewMode();
        console.log('  恢复原始视图模式');
    }
    
    // 5. 最终结果
    console.log('\n📊 测试结果汇总:');
    console.log('  场景A（子分类→常用）:', testA_pass ? '✅ 通过' : '❌ 失败');
    console.log('  场景B（全部分类→常用）:', testB_pass ? '✅ 通过' : '❌ 失败');
    console.log('  场景C（视图切换）:', testC_pass ? '✅ 通过' : '❌ 失败');
    
    const allTestsPass = testA_pass && testB_pass && testC_pass;
    console.log('\n🎯 总体结果:', allTestsPass ? '✅ 所有测试通过，Bug已修复' : '❌ 部分测试失败，需要进一步调试');
    
    if (!allTestsPass) {
        console.log('\n🔧 调试信息:');
        console.log('  如果测试失败，请检查以下方法是否正确实现:');
        console.log('  - ensureStateConsistency()');
        console.log('  - refreshCurrentView()');
        console.log('  - switchCategory() 对 frequent 的处理');
    }
    
}, 200);

console.log('\n⏳ 等待视图切换测试完成...');
