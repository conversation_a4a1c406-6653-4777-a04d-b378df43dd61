<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试常用分类问题</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>常用分类问题测试</h1>
        
        <div class="test-section">
            <h3>测试步骤</h3>
            <p>1. 打开主页面 (http://localhost:3000)</p>
            <p>2. 点击侧边栏的"常用"分类</p>
            <p>3. 检查内容区域是否显示正确的常用内容，而不是全部分类的内容</p>
            
            <button class="test-button" onclick="openMainPage()">打开主页面</button>
            <button class="test-button" onclick="checkConsole()">检查控制台输出</button>
        </div>
        
        <div class="test-section">
            <h3>调试信息</h3>
            <button class="test-button" onclick="testFrequentCategory()">获取调试命令</button>
            <button class="test-button" onclick="testValidateCategory()">测试分类验证</button>
            <button class="test-button" onclick="clearVisitHistory()">清除访问历史</button>
            <button class="test-button" onclick="simulateVisits()">模拟访问记录</button>
            <button class="test-button" onclick="showQuickTest()">快速测试步骤</button>

            <div id="testResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>预期行为</h3>
            <ul>
                <li><strong>有访问历史时：</strong>点击"常用"应显示最近访问的网站</li>
                <li><strong>无访问历史时：</strong>点击"常用"应显示空状态提示</li>
                <li><strong>不应该：</strong>显示"全部分类"的内容（如"服务发布"、"配置修改"等分类标题）</li>
            </ul>
        </div>
    </div>

    <script>
        function openMainPage() {
            window.open('http://localhost:3000', '_blank');
        }
        
        function checkConsole() {
            const result = document.getElementById('testResult');
            result.textContent = '请在主页面按F12打开开发者工具，查看Console标签页的输出。\n\n关键日志：\n- "设置常用分类状态: isShowingAllCategories = false"\n- "切换到常用分类"\n- "常用分类返回 X 个网站"';
        }
        
        function testFrequentCategory() {
            const result = document.getElementById('testResult');
            result.className = 'result';
            result.textContent = '请在主页面控制台中运行以下调试命令：\n\n// 1. 检查当前状态\nconsole.log("当前分类:", navApp.currentCategory);\nconsole.log("显示所有分类模式:", navApp.isShowingAllCategories);\nconsole.log("常用网站数量:", navApp.getFrequentSites().length);\n\n// 2. 测试切换到常用分类\nnavApp.switchCategory("frequent");\n\n// 3. 检查切换后的状态\nconsole.log("切换后 - 当前分类:", navApp.currentCategory);\nconsole.log("切换后 - 显示所有分类模式:", navApp.isShowingAllCategories);\n\n// 4. 检查分类信息\nconst category = navApp.getCategoryById("frequent");\nconsole.log("常用分类信息:", category);\nconsole.log("是否为虚拟分类:", category.isVirtual);\n\n// 5. 检查网站容器的CSS类\nconst container = document.querySelector(".sites-container");\nconsole.log("容器CSS类:", container.className);\n\n// 6. 检查内容区域的HTML（前100个字符）\nconsole.log("内容区域HTML:", container.innerHTML.substring(0, 100));';
        }
        
        function testValidateCategory() {
            const result = document.getElementById('testResult');
            result.textContent = '请在主页面控制台中运行以下命令测试：\n\n// 测试常用分类验证\nnavApp.validateAndFixCategory("frequent")\n\n// 测试切换到常用分类\nnavApp.switchCategory("frequent")\n\n// 检查状态\nconsole.log("当前分类:", navApp.currentCategory)\nconsole.log("显示所有分类:", navApp.isShowingAllCategories)';
        }
        
        function clearVisitHistory() {
            const result = document.getElementById('testResult');
            result.textContent = '请在主页面控制台中运行：\nnavApp.visitManager.clearHistory()\n\n然后刷新页面并点击"常用"分类测试';
        }
        
        function simulateVisits() {
            const result = document.getElementById('testResult');
            result.textContent = '请在主页面控制台中运行：\n\n// 模拟一些访问记录\nnavApp.visitManager.recordVisit("k8s-app", "发布系统", "external")\nnavApp.visitManager.recordVisit("git-repo", "GitLab", "external")\nnavApp.visitManager.recordVisit("wiki", "WIKI", "external")\n\n// 然后点击"常用"分类查看结果\nnavApp.switchCategory("frequent")';
        }

        function showQuickTest() {
            const result = document.getElementById('testResult');
            result.className = 'result';
            result.textContent = `快速测试步骤：

1. 打开主页面 (http://localhost:3000)
2. 按F12打开开发者工具，切换到Console标签页
3. 运行以下命令来模拟访问记录：

navApp.visitManager.recordVisit("k8s-app", "发布系统", "external");
navApp.visitManager.recordVisit("git-repo", "GitLab", "external");

4. 点击侧边栏的"常用"分类
5. 检查内容区域是否显示：
   - ✅ 正确：显示"发布系统"和"GitLab"两个网站卡片
   - ❌ 错误：显示"服务发布"、"配置修改"等分类标题

6. 如果显示错误，在控制台运行调试命令：
console.log("当前分类:", navApp.currentCategory);
console.log("显示所有分类模式:", navApp.isShowingAllCategories);

预期结果：
- 当前分类: "frequent"
- 显示所有分类模式: false`;
        }
    </script>
</body>
</html>
